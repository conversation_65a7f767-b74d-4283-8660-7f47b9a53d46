import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Input, Label, Select, Text } from "@saf/ui"
import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

const alertTypeOptions: Options = [
  {
    label: "Info",
    value: "info",
  },
  {
    label: "Warning",
    value: "warning",
  },
  {
    label: "Error",
    value: "error",
  },
  {
    label: "Success",
    value: "success",
  },
]

const alertEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    title: z.string().min(1, "Title is required"),
    subtitle: z.string().optional(),
    type: z.enum(createEnumFromOptions(alertTypeOptions)),
  }),
)

type AlertEditorSchema = z.infer<typeof alertEditorSchema>

export const AlertEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["AlertWidget"]
  onUpdate: (updatedData: components["schemas"]["AlertWidget"]) => void
}) => {
  const form = useForm<AlertEditorSchema>({
    resolver: zodResolver(alertEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      title: "",
      subtitle: "",
      type: "info",
    },
  })

  useEffect(() => {
    if (data) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        title: data.config?.title || "",
        subtitle: data.config?.subtitle || "",
        type: data.config?.design?.type || "info",
      })
    }
  }, [data, form])

  useEffect(() => {
    if (!data) return

    const subscription = form.watch((values) => {
      const updatedData: components["schemas"]["AlertWidget"] = {
        ...data,
        name: values.name || "",
        isHidden: values.isHidden ?? false,
        config: {
          ...data.config,
          title: values.title || "",
          subtitle: values.subtitle || "",
          design: {
            type: values.type as components["schemas"]["AlertWidget"]["config"]["design"]["type"],
          },
        },
      }
      onUpdate(updatedData)
    })
    return () => subscription.unsubscribe()
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Content
          </Text>
          <Field control={form.control} name="title" label="Title">
            <Input />
          </Field>
          <Field control={form.control} name="subtitle" label="Subtitle (Optional)">
            <Input />
          </Field>
        </div>
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <Label className="flex items-center justify-between gap-4">
            <span>Alert Type</span>
            <div className="w-full max-w-[170px]">
              <Field {...form} name="type">
                <Select onValueChange={form.setValue.bind(null, "type")}>
                  <Select.Trigger>
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    {alertTypeOptions.map((item) => (
                      <Select.Item key={item.value} value={item.value}>
                        {item.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </Field>
            </div>
          </Label>
        </div>
      </form>
    </Form>
  )
}
